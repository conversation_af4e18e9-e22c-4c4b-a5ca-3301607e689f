import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { FileText, Globe, File, Download, ExternalLink, Calendar, User, Tag, AlertCircle, Edit, Trash2, TreePine, Link, ChevronRight, Users, Clock, BarChart3 } from 'lucide-react';
import { Button, Badge, Card, CardContent, CardHeader, CardTitle } from 'components/ui';
import { Skeleton } from '../../../components/ui/skeleton';
import resourceService from '../../../services/resourceService';

const ViewResource = () => {
  const { id, type } = useParams();
  const [resource, setResource] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [docViewerError, setDocViewerError] = useState(false);
  const [hierarchyData, setHierarchyData] = useState(null);
  const [loadingHierarchy, setLoadingHierarchy] = useState(false);

  console.log('=== VIEWRESOURCE COMPONENT MOUNTED ===');
  console.log('URL params - id:', id, 'type:', type);
  console.log('Component state - loading:', loading, 'error:', error, 'resource:', resource);
  
  // Temporary test to ensure component is rendering
  console.log('ViewResource component is definitely running!');
  
  // Test alert to verify component is loaded (remove this after testing)
  if (typeof window !== 'undefined') {
    console.log('Component loaded at:', new Date().toISOString());
  }

  // Function to fetch hierarchy data for web resources
  const fetchHierarchyData = async (webResource) => {
    if (!webResource || !webResource.id) return;

    try {
      setLoadingHierarchy(true);

      // Fetch children and hierarchy data
      const [childrenResponse, hierarchyResponse] = await Promise.all([
        resourceService.getWebResourceChildren(webResource.id),
        resourceService.getWebResourceHierarchy(webResource.id)
      ]);

      setHierarchyData({
        children: childrenResponse?.children || [],
        hierarchy: hierarchyResponse?.hierarchy || [],
        parent: webResource.parent_url ? hierarchyResponse?.parent : null,
        isRoot: !webResource.parent_url,
        crawlStrategy: webResource.crawl_strategy || 'single'
      });
    } catch (error) {
      console.error('Failed to fetch hierarchy data:', error);
      // Don't set error state for hierarchy - it's not critical
    } finally {
      setLoadingHierarchy(false);
    }
  };

  useEffect(() => {
    const fetchResource = async () => {
      try {
        setLoading(true);
        setError(null);
        
        console.log('=== FETCHING RESOURCE ===');
        console.log('Type from URL:', type);
        console.log('ID from URL:', id);
        
        // Use the resource type from URL to make the correct API call
        let fetchedResource;
        switch (type) {
          case 'file':
            console.log('Calling resourceService.getFileResource');
            fetchedResource = await resourceService.getFileResource(id);
            break;
          case 'text':
          case 'article': // Handle both URL type ('text') and backend type ('article')
            console.log('Calling resourceService.getTextResource');
            fetchedResource = await resourceService.getTextResource(id);
            break;
          case 'web':
            console.log('Calling resourceService.getWebResource');
            fetchedResource = await resourceService.getWebResource(id);
            break;
          default:
            console.error('Unknown resource type received:', type);
            throw new Error(`Unknown resource type: ${type}`);
        }
        
        console.log('=== RESOURCE FETCHED ===');
        console.log('Raw API response:', fetchedResource);
        console.log('Resource keys:', Object.keys(fetchedResource || {}));
        
        // Extract the actual resource data from the API response
        const actualResource = fetchedResource?.resource || fetchedResource;
        console.log('Actual resource data:', actualResource);
        console.log('Actual resource keys:', Object.keys(actualResource || {}));
        
        setResource(actualResource);
        console.log('Resource set in state');

        // Fetch hierarchy data for web resources
        if (type === 'web' && actualResource) {
          await fetchHierarchyData(actualResource);
        }
      } catch (err) {
        console.error('=== ERROR FETCHING RESOURCE ===');
        console.error('Error details:', err);
        console.error('Error message:', err.message);
        console.error('Error stack:', err.stack);
        setError(err.message || 'Failed to load resource');
      } finally {
        setLoading(false);
        console.log('Loading set to false');
      }
    };

    console.log('=== USEEFFECT TRIGGERED ===');
    console.log('ID:', id, 'Type:', type);
    
    if (id && type) {
      console.log('ID and type are present, fetching resource...');
      fetchResource();
    } else {
      console.log('Missing ID or type:', { id, type });
    }
  }, [id, type]);

  const handleEdit = () => {
    // TODO: Navigate to edit page or open edit modal
    console.log('Edit resource:', displayResource?.id);
    // navigate(`/resources/${type}/${id}/edit`);
  };

  const handleDelete = async () => {
    // TODO: Show confirmation dialog and handle delete
    console.log('Delete resource:', displayResource?.id);
    if (window.confirm('Are you sure you want to delete this resource?')) {
      try {
        const resourceType = type === 'text' ? 'article' : type;
        await resourceService.deleteResource(resourceType, id);
        // TODO: Navigate back to resources list
        // navigate('/resources');
      } catch (error) {
        console.error('Error deleting resource:', error);
        // TODO: Show error toast
      }
    }
  };

  const getResourceIcon = (resourceType) => {
    switch (resourceType) {
      case 'file':
        return <File className="w-5 h-5" />;
      case 'text':
        return <FileText className="w-5 h-5" />;
      case 'web':
        return <Globe className="w-5 h-5" />;
      default:
        return <File className="w-5 h-5" />;
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatFileSize = (bytes) => {
    if (!bytes) return 'N/A';
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return `${(bytes / Math.pow(1024, i)).toFixed(1)} ${sizes[i]}`;
  };

  // Helper to get the correct file URL
  const getFileUrl = (resource) => {
    if (!resource) return null;
    if (resource.file_url && (resource.file_url.startsWith('http://') || resource.file_url.startsWith('https://') || resource.file_url.startsWith('/api/files/'))) {
      return resource.file_url;
    }
    if (resource.storage_key) {
      return `/api/files/${resource.storage_key}`;
    }
    return null;
  };

  if (loading) {
    console.log('=== COMPONENT STATE: LOADING ===');
    return (
      <div className="container mx-auto px-4 py-6 space-y-6">
        <div className="flex items-center space-x-4">
          <Skeleton className="w-8 h-8" />
          <Skeleton className="h-8 w-48" />
        </div>
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-2">
            <Skeleton className="h-96 w-full" />
          </div>
          <div className="space-y-4">
            <Skeleton className="h-32 w-full" />
            <Skeleton className="h-48 w-full" />
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    console.log('=== COMPONENT STATE: ERROR ===');
    console.log('Error message:', error);
    return (
      <div className="container mx-auto px-4 py-6">
        <div className="bg-surface rounded-lg border p-6">
          <div className="text-center">
            <div className="text-red-500 mb-4">
              <File className="w-12 h-12 mx-auto" />
            </div>
            <h3 className="text-lg font-semibold mb-2">Error Loading Resource</h3>
            <p className="text-text-secondary">{error}</p>
          </div>
        </div>
      </div>
    );
  }

  if (!resource) {
    console.log('=== COMPONENT STATE: NO RESOURCE ===');
    console.log('Resource is null or undefined');
    return (
      <div className="container mx-auto px-4 py-6">
        <div className="bg-surface rounded-lg border p-6">
          <div className="text-center">
            <h3 className="text-lg font-semibold mb-2">Resource Not Found</h3>
            <p className="text-text-secondary">The requested resource could not be found.</p>
          </div>
        </div>
      </div>
    );
  }

  console.log('=== COMPONENT STATE: RENDERING ===');
  console.log('Resource object:', resource);
  console.log('URL type:', type);
  console.log('Resource content fields:', Object.keys(resource || {}));
  console.log('Resource title:', resource?.title);
  console.log('Resource content:', resource?.content);
  console.log('Resource status:', resource?.status);
  console.log('Resource file_url:', resource?.file_url);
  console.log('Resource storage_key:', resource?.storage_key);
  console.log('Resource file_type:', resource?.file_type);
  console.log('Generated file URL:', getFileUrl(resource));

  // Use the type from URL params since the API response might not include it
  const resourceType = type === 'text' ? 'article' : type;
  const displayResource = {
    ...resource,
    type: resourceType, // Ensure type is set for display logic
    file_type: resource.file_type || resource.fileType // Handle both field names
  };

  return (
    <div className="container mx-auto px-4 py-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-primary/10 rounded-lg text-primary">
            {getResourceIcon(displayResource.type)}
          </div>
          <div>
            <h1 className="text-2xl font-bold text-text-primary">{displayResource.title || displayResource.name || 'Untitled Resource'}</h1>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          {displayResource.type === 'file' && getFileUrl(displayResource) && (
            <Button variant="outline" size="sm" asChild>
              <a href={getFileUrl(displayResource)} download>
                <Download className="w-4 h-4 mr-2" />
                Download
              </a>
            </Button>
          )}
          {displayResource.type === 'web' && displayResource.url && (
            <Button variant="outline" size="sm" asChild>
              <a href={displayResource.url} target="_blank" rel="noopener noreferrer">
                <ExternalLink className="w-4 h-4 mr-2" />
                Visit
              </a>
            </Button>
          )}
          
          {/* Action Buttons */}
          <Button variant="outline" size="sm" onClick={handleEdit}>
            <Edit className="w-4 h-4 mr-2" />
            Edit
          </Button>
          <Button variant="outline" size="sm" color="red" onClick={handleDelete}>
            <Trash2 className="w-4 h-4 mr-2" />
            Delete
          </Button>
        </div>
      </div>

      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Content Area */}
        <div className="lg:col-span-2">
            {/* File Content */}
            {displayResource.type === 'file' && (
              <>
                {getFileUrl(displayResource) ? (
                  <div className="h-[80vh] rounded-lg border overflow-hidden">
                    {displayResource.status === 'failed' && (
                      <div className="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                        <div className="flex items-center">
                          <AlertCircle className="w-4 h-4 text-yellow-600 mr-2" />
                          <span className="text-sm text-yellow-800">
                            File processing failed, but the file is available for viewing/download.
                          </span>
                        </div>
                      </div>
                    )}
                    
                    {!docViewerError ? (
                      <iframe
                        src={getFileUrl(displayResource)}
                        className="w-full h-full border-0"
                        title={displayResource.name || 'File Preview'}
                        onError={() => {
                          console.error('File preview error');
                          setDocViewerError(true);
                        }}
                      />
                    ) : (
                      <div className="border rounded-lg h-full flex items-center justify-center bg-gray-50">
                        <div className="text-center">
                          <File className="w-12 h-12 mx-auto mb-4 text-gray-400" />
                          <p className="text-gray-600 mb-4">Unable to preview this file format</p>
                          <div className="space-x-2">
                            <Button 
                              onClick={() => window.open(getFileUrl(displayResource), '_blank')}
                              variant="outline"
                            >
                              <ExternalLink className="w-4 h-4 mr-2" />
                              Open in New Tab
                            </Button>
                            <Button 
                              onClick={() => setDocViewerError(false)}
                              variant="ghost"
                              size="sm"
                            >
                              Try Again
                            </Button>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="text-center text-gray-500 py-12">
                    <File className="w-12 h-12 mx-auto mb-4 opacity-50" />
                    <p>No file content available</p>
                  </div>
                )}
              </>
            )}

            {/* Text/Article Content */}
            {(displayResource.type === 'text' || displayResource.type === 'article') && (
              <>
                {displayResource.content || displayResource.text_content ? (
                  <div className="h-[80vh] overflow-y-auto rounded-lg border p-4 bg-gray-50">
                    <div className="whitespace-pre-wrap font-sans text-sm leading-relaxed text-gray-900">
                      {displayResource.content || displayResource.text_content}
                    </div>
                  </div>
                ) : (
                  <div className="text-center text-gray-500 py-12">
                    <FileText className="w-12 h-12 mx-auto mb-4 opacity-50" />
                    <p>No text content available</p>
                  </div>
                )}
              </>
            )}

            {/* Web Content */}
            {displayResource.type === 'web' && (
              <>
                {displayResource.url ? (
                  <div className="space-y-4">
                    <div>
                      <h4 className="font-semibold mb-2">URL</h4>
                      <a
                        href={displayResource.url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-primary hover:underline break-all"
                      >
                        {displayResource.url}
                      </a>
                    </div>

                    {/* Web Resource Hierarchy */}
                    {hierarchyData && (
                      <div className="space-y-4">
                        {/* Crawl Strategy Info */}
                        <div>
                          <h4 className="font-semibold mb-2">Crawl Information</h4>
                          <div className="flex items-center space-x-4 text-sm">
                            <Badge variant="outline">
                              {hierarchyData.crawlStrategy === 'single' ? 'Single Page' :
                               hierarchyData.crawlStrategy === 'recursive' ? 'Recursive Crawl' :
                               hierarchyData.crawlStrategy === 'sitemap' ? 'Sitemap Crawl' :
                               'Unknown'}
                            </Badge>
                            {hierarchyData.isRoot && (
                              <Badge variant="soft" color="blue">
                                <TreePine className="w-3 h-3 mr-1" />
                                Root Resource
                              </Badge>
                            )}
                            {hierarchyData.childCount > 0 && (
                              <Badge variant="soft" color="green">
                                <Users className="w-3 h-3 mr-1" />
                                {hierarchyData.childCount} child pages
                              </Badge>
                            )}
                          </div>
                        </div>

                        {/* Parent Resource */}
                        {hierarchyData.parent && !hierarchyData.isRoot && (
                          <div>
                            <h4 className="font-semibold mb-2">Parent Resource</h4>
                            <div className="flex items-center space-x-2 p-3 bg-blue-50 rounded-lg border border-blue-200">
                              <Link className="w-4 h-4 text-blue-600" />
                              <div className="flex-1">
                                <p className="text-sm font-medium text-blue-900">
                                  {hierarchyData.parent.title || hierarchyData.parent.name}
                                </p>
                                <p className="text-xs text-blue-700 break-all">
                                  {hierarchyData.parent.url}
                                </p>
                              </div>
                              <Button
                                size="sm"
                                variant="ghost"
                                onClick={() => window.open(`/resources/web/${hierarchyData.parent.id}`, '_blank')}
                              >
                                <ExternalLink className="w-3 h-3" />
                              </Button>
                            </div>
                          </div>
                        )}

                        {/* Child Resources */}
                        {hierarchyData.children && hierarchyData.children.length > 0 && (
                          <div>
                            <h4 className="font-semibold mb-2">
                              Child Pages ({hierarchyData.children.length})
                            </h4>
                            <div className="space-y-2 max-h-60 overflow-y-auto">
                              {hierarchyData.children.slice(0, 10).map((child) => (
                                <div key={child.id} className="flex items-center space-x-2 p-2 bg-gray-50 rounded border">
                                  <Globe className="w-3 h-3 text-gray-500 flex-shrink-0" />
                                  <div className="flex-1 min-w-0">
                                    <p className="text-sm font-medium text-gray-900 truncate">
                                      {child.title || child.name}
                                    </p>
                                    <p className="text-xs text-gray-600 truncate">
                                      {child.url}
                                    </p>
                                  </div>
                                  <Button
                                    size="sm"
                                    variant="ghost"
                                    onClick={() => window.open(`/resources/web/${child.id}`, '_blank')}
                                  >
                                    <ExternalLink className="w-3 h-3" />
                                  </Button>
                                </div>
                              ))}
                              {hierarchyData.children.length > 10 && (
                                <div className="text-center py-2">
                                  <p className="text-sm text-gray-500">
                                    ... and {hierarchyData.children.length - 10} more child pages
                                  </p>
                                </div>
                              )}
                            </div>
                          </div>
                        )}
                      </div>
                    )}

                    {(displayResource.web_content || displayResource.content) && (
                      <div>
                        <h4 className="font-semibold mb-2">Content</h4>
                        <div className="h-[80vh] overflow-y-auto rounded-lg border p-4 bg-gray-50">
                          <div 
                            className="text-sm leading-relaxed"
                            dangerouslySetInnerHTML={{ __html: displayResource.web_content || displayResource.content }}
                          />
                        </div>
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="text-center text-gray-500 py-12">
                    <Globe className="w-12 h-12 mx-auto mb-4 opacity-50" />
                    <p>No web content available</p>
                  </div>
                )}
              </>
            )}

            {/* Fallback for unknown resource types */}
            {!['file', 'text', 'article', 'web'].includes(displayResource.type) && (
              <div className="text-center text-gray-500 py-12">
                <File className="w-12 h-12 mx-auto mb-4 opacity-50" />
                <p>Unsupported resource type: {displayResource.type || 'unknown'}</p>
              </div>
            )}
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Resource Info */}
          <div className="bg-surface rounded-lg border p-6">
            <div className="flex items-center space-x-2 mb-4">
              <h3 className="text-lg font-semibold text-text-primary">Resource Information</h3>
            </div>
            <div className="space-y-4">
              {displayResource.status && (
                <div className="flex items-center space-x-3">
                  <AlertCircle className="w-4 h-4 text-text-tertiary" />
                  <div>
                    <p className="text-sm font-medium">Status</p>
                    <Badge 
                      variant={displayResource.status === 'processed' ? 'soft' : 
                              displayResource.status === 'processing' ? 'surface' : 
                              displayResource.status === 'failed' ? 'solid' : 'soft'}
                      color={displayResource.status === 'processed' ? 'green' : 
                             displayResource.status === 'processing' ? 'orange' : 
                             displayResource.status === 'failed' ? 'red' : 'gray'}
                    >
                      {displayResource.status}
                    </Badge>
                  </div>
                </div>
              )}
              
              <div className="flex items-center space-x-3">
                <Calendar className="w-4 h-4 text-text-tertiary" />
                <div>
                  <p className="text-sm font-medium">Created</p>
                  <p className="text-sm text-text-secondary">{formatDate(displayResource.created_at)}</p>
                </div>
              </div>
              
              {displayResource.updated_at && (
                <div className="flex items-center space-x-3">
                  <Calendar className="w-4 h-4 text-text-tertiary" />
                  <div>
                    <p className="text-sm font-medium">Updated</p>
                    <p className="text-sm text-text-secondary">{formatDate(displayResource.updated_at)}</p>
                  </div>
                </div>
              )}
              
              {displayResource.created_by && (
                <div className="flex items-center space-x-3">
                  <User className="w-4 h-4 text-text-tertiary" />
                  <div>
                    <p className="text-sm font-medium">Created By</p>
                    <p className="text-sm text-text-secondary">{displayResource.created_by}</p>
                  </div>
                </div>
              )}
              
              {displayResource.type === 'file' && displayResource.size && (
                <div className="flex items-center space-x-3">
                  <File className="w-4 h-4 text-text-tertiary" />
                  <div>
                    <p className="text-sm font-medium">File Size</p>
                    <p className="text-sm text-text-secondary">{formatFileSize(displayResource.size)}</p>
                  </div>
                </div>
              )}
              
              {displayResource.type === 'file' && displayResource.file_type && (
                <div className="flex items-center space-x-3">
                  <File className="w-4 h-4 text-text-tertiary" />
                  <div>
                    <p className="text-sm font-medium">File Type</p>
                    <p className="text-sm text-text-secondary">{displayResource.file_type}</p>
                  </div>
                </div>
              )}

              {/* Web Resource Specific Fields */}
              {displayResource.type === 'web' && displayResource.last_crawled && (
                <div className="flex items-center space-x-3">
                  <Clock className="w-4 h-4 text-text-tertiary" />
                  <div>
                    <p className="text-sm font-medium">Last Crawled</p>
                    <p className="text-sm text-text-secondary">{formatDate(displayResource.last_crawled)}</p>
                  </div>
                </div>
              )}

              {displayResource.type === 'web' && displayResource.crawl_strategy && (
                <div className="flex items-center space-x-3">
                  <BarChart3 className="w-4 h-4 text-text-tertiary" />
                  <div>
                    <p className="text-sm font-medium">Crawl Strategy</p>
                    <p className="text-sm text-text-secondary">
                      {displayResource.crawl_strategy === 'single' ? 'Single Page' :
                       displayResource.crawl_strategy === 'recursive' ? 'Recursive Crawl' :
                       displayResource.crawl_strategy === 'sitemap' ? 'Sitemap Crawl' :
                       displayResource.crawl_strategy}
                    </p>
                  </div>
                </div>
              )}

              {displayResource.type === 'web' && displayResource.crawl_depth !== undefined && (
                <div className="flex items-center space-x-3">
                  <TreePine className="w-4 h-4 text-text-tertiary" />
                  <div>
                    <p className="text-sm font-medium">Crawl Depth</p>
                    <p className="text-sm text-text-secondary">{displayResource.crawl_depth}</p>
                  </div>
                </div>
              )}

              {displayResource.type === 'web' && hierarchyData && hierarchyData.childCount > 0 && (
                <div className="flex items-center space-x-3">
                  <Users className="w-4 h-4 text-text-tertiary" />
                  <div>
                    <p className="text-sm font-medium">Child Pages</p>
                    <p className="text-sm text-text-secondary">{hierarchyData.childCount}</p>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Tags */}
          {displayResource.tags && displayResource.tags.length > 0 && (
            <div className="bg-surface rounded-lg border p-6">
              <div className="flex items-center space-x-2 mb-4">
                <h3 className="text-lg font-semibold text-text-primary">Tags</h3>
              </div>
              <div className="flex flex-wrap gap-2">
                {displayResource.tags.map((tag, index) => (
                  <Badge key={index} variant="soft" color="blue">
                    {tag}
                  </Badge>
                ))}
              </div>
            </div>
          )}

          {/* Metadata */}
          {displayResource.metadata && Object.keys(displayResource.metadata).length > 0 && (
            <div className="bg-surface rounded-lg border p-6">
              <div className="flex items-center space-x-2 mb-4">
                <h3 className="text-lg font-semibold text-text-primary">Metadata</h3>
              </div>
              <div className="space-y-2">
                {Object.entries(displayResource.metadata).map(([key, value]) => (
                  <div key={key} className="flex justify-between text-sm">
                    <span className="font-medium text-text-secondary">{key}:</span>
                    <span className="text-text-primary">{String(value)}</span>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ViewResource;
