from fastapi import APIRouter, UploadFile, File, Form, HTTPException, Depends, status, Query
from fastapi.responses import J<PERSON><PERSON>esponse
from typing import Optional, Literal
import asyncio
from services.resource_service import ResourceService
from models.role import Permission
from services.service_manager import get_resource_service
from services.task_scheduler import TaskScheduler
from models.resource import ResourceType, ResourceStatus
from models.task import TaskType
from security.unified_auth_middleware import get_current_user, requires_permissions, create_running_context
from security.auth_middleware import StatelessUser, StatelessRunningContext
from uuid import uuid4
import logging
import os
from uuid import UUID

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/resources", tags=["resources"])
scheduler = TaskScheduler()  # Initialize the task scheduler

# Define permission requirements for routes
create_resource_permission = requires_permissions({Permission.CREATE_RESOURCE})
read_resource_permission = requires_permissions({Permission.READ_RESOURCE})
update_resource_permission = requires_permissions({Permission.UPDATE_RESOURCE})
delete_resource_permission = requires_permissions({Permission.DELETE_RESOURCE})
search_resource_permission = requires_permissions({Permission.SEARCH_RESOURCE})

UPLOAD_DIR = "uploads"
os.makedirs(UPLOAD_DIR, exist_ok=True)

# Dependency to get resource service
async def get_resource_service_dep() -> ResourceService:
    """Dependency to get the resource service"""
    return await get_resource_service()

# File Resource Endpoint
@router.post("/files")
async def add_file_resource_endpoint(
    title: str = Form(...),
    description: Optional[str] = Form(None),
    file: UploadFile = File(...),
    process_immediately: bool = Form(False),  # Force immediate processing
    schedule: Optional[str] = Form(None),  # Optional cron schedule for periodic processing
    current_user: StatelessUser = Depends(get_current_user),
    permission_check: StatelessUser = Depends(requires_permissions()),
    context: StatelessRunningContext = Depends(create_running_context),
    resource_service = Depends(get_resource_service_dep)
):
    """Upload and process a file resource with flexible storage and processing"""
    tenant_id = current_user.tenant_id
    if not tenant_id:
        raise HTTPException(status_code=400, detail="Tenant id missing in user context")
    
    # Validate file
    if not file.filename:
        raise HTTPException(status_code=400, detail="File must have a filename")
    
    try:
        # Upload and process file using the new storage system
        result = await resource_service.upload_and_process_file(
            file=file,
            current_user=current_user,
            tenant_id=tenant_id,
            title=title,
            metadata={
                "title":title,
                "description": description,
                "Upload-Source": "api"
            },
            process_immediately=process_immediately
        )
        
        # Schedule periodic processing if requested
        task_id = None
        if schedule:
            try:
                task_id = await scheduler.schedule_task(
                    resource_id=result["resource_id"],
                    task_type=TaskType.FILE_PROCESSING,
                    tenant_id=tenant_id,
                    data={"file_path": result["storage_key"]},
                    cron_expression=schedule
                )
                logger.info(f"Scheduled periodic processing for resource {result['resource_id']}")
            except Exception as e:
                logger.warning(f"Failed to schedule task: {str(e)}")
        
        return JSONResponse(
            status_code=status.HTTP_201_CREATED,
            content={
                "message": "File uploaded and processing initiated",
                "resource_id": result["resource_id"],
                "storage_key": result["storage_key"],
                "filename": result["filename"],
                "size": result["size"],
                "content_type": result["content_type"],
                "processing_strategy": result["processing_strategy"],
                "processing_status": result["processing_result"].get("status"),
                "task_id": task_id,
                "details": result
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"File upload failed: {str(e)}")
        raise HTTPException(status_code=500, detail=f"File upload failed: {str(e)}")


@router.get("/files/{resource_id}/download")
async def download_file_endpoint(
    resource_id: str,
    current_user: StatelessUser = Depends(get_current_user),
    resource_service = Depends(get_resource_service_dep)
):
    """Download a file from storage"""
    try:
        file_data = await resource_service.download_file(resource_id, current_user)
        
        from fastapi.responses import StreamingResponse
        import io
        
        return StreamingResponse(
            io.BytesIO(file_data["content"]),
            media_type=file_data["content_type"],
            headers={
                "Content-Disposition": f"attachment; filename=\"{file_data['filename']}\""
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"File download failed: {str(e)}")
        raise HTTPException(status_code=500, detail="Download failed")


@router.get("/files/{resource_id}/download-url")
async def get_file_download_url_endpoint(
    resource_id: str,
    expiration: int = 3600,
    current_user: StatelessUser = Depends(get_current_user),
    resource_service = Depends(get_resource_service_dep)
):
    """Get a presigned download URL for a file"""
    try:
        url = await resource_service.get_file_download_url(
            resource_id, current_user, expiration
        )
        
        return JSONResponse({
            "download_url": url,
            "expires_in": expiration,
            "resource_id": resource_id
        })
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Download URL generation failed: {str(e)}")
        raise HTTPException(status_code=500, detail="URL generation failed")


@router.delete("/files/{resource_id}")
async def delete_file_endpoint(
    resource_id: str,
    current_user: StatelessUser = Depends(get_current_user),
    resource_service = Depends(get_resource_service_dep)
):
    """Delete a file from both database and storage"""
    try:
        success = await resource_service.delete_file_from_storage(resource_id, current_user)
        
        if success:
            return JSONResponse({
                "message": "File deleted successfully",
                "resource_id": resource_id
            })
        else:
            raise HTTPException(status_code=404, detail="File not found")
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"File deletion failed: {str(e)}")
        raise HTTPException(status_code=500, detail="Deletion failed")


@router.get("/storage/stats")
async def get_storage_stats_endpoint(
    current_user: StatelessUser = Depends(get_current_user),
    resource_service = Depends(get_resource_service_dep)
):
    """Get storage statistics for the current tenant"""
    try:
        # For system users, allow access without tenant restriction
        has_system_permission = current_user.is_system_admin()
        if has_system_permission:
            tenant_id = current_user.tenant_id or uuid4()  # Use a default tenant for system users
        else:
            tenant_id = current_user.tenant_id
            if not tenant_id:
                raise HTTPException(status_code=400, detail="Tenant ID missing")
        
        stats = await resource_service.get_storage_stats(tenant_id, current_user)
        
        return JSONResponse({
            "message": "Storage statistics retrieved",
            "stats": stats
        })
        
    except Exception as e:
        logger.error(f"Storage stats failed: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to get storage stats")


@router.get("/storage/health")
async def get_storage_health_endpoint():
    """Get storage backend health status"""
    try:
        from services.storage import storage_manager
        
        health = await storage_manager.health_check()
        
        return JSONResponse({
            "message": "Storage health check completed",
            "health": health
        })
        
    except Exception as e:
        logger.error(f"Storage health check failed: {str(e)}")
        raise HTTPException(status_code=500, detail="Health check failed")

# Article Resource Endpoint
@router.post("/articles")
async def add_article_resource_endpoint(
    title: str = Form(...),
    description: Optional[str] = Form(None),
    content: str = Form(...),
    author: Optional[str] = Form(None),
    tags: Optional[str] = Form(None),  # Comma-separated tags
    current_user: StatelessUser = Depends(get_current_user),
    permission_check: StatelessUser = Depends(requires_permissions()),
    context: StatelessRunningContext = Depends(create_running_context),
    resource_service: ResourceService = Depends(get_resource_service_dep)
):
    """Add and process a text/article resource"""
    tenant_id = current_user.tenant_id
    if not tenant_id:
        raise HTTPException(status_code=400, detail="Tenant id missing in user context")
    
    # Parse tags
    tags_list = [tag.strip() for tag in tags.split(",")] if tags else []
    
    # Create the article resource record
    from models.resource import ArticleResource
    resource_data = ArticleResource(
        title=title,
        description=description,
        owner_id=current_user.id,
        tenant_id=current_user.tenant_id,
        content=content,
        author=author,
        tags=tags_list
    )
    
    resource = await resource_service.create_article_resource(
        resource=resource_data,
        current_user=current_user,
        tenant_id=None
    )
    
    try:
        # Process the article using the comprehensive indexer
        result = await resource_service.process_article_resource(
            text=content,
            resource_id=resource.id,
            current_user=current_user,
            tenant_id=current_user.tenant_id,
            metadata={"title": title}
        )
        
        return JSONResponse({
            "message": "Article resource added and processed successfully",
            "resource": resource.model_dump(),
            "processing_result": result
        }, status_code=201)
        
    except Exception as e:
        logger.error(f"Error processing article resource: {str(e)}")
        # Update resource status to failed
        await resource_service.update_resource_status(
            resource_type="article",
            resource_id=resource.id,
            status=ResourceStatus.FAILED,
            message=f"Processing failed: {str(e)}"
        )
        raise HTTPException(status_code=500, detail=f"Failed to process article: {str(e)}")

# Web Resource Endpoint
@router.post("/web")
async def add_web_resource_endpoint(
    title: str = Form(...),
    description: Optional[str] = Form(None),
    url: str = Form(...),
    load_type: Literal["single", "recursive", "sitemap"] = Form("single"),
    max_depth: Optional[int] = Form(2),
    exclude_dirs: Optional[str] = Form(None),  # Comma-separated directories to exclude
    sitemap_url: Optional[str] = Form(None),
    process_immediately: bool = Form(True),  # Whether to process large sitemaps immediately
    current_user: StatelessUser = Depends(get_current_user),
    permission_check: StatelessUser = Depends(create_resource_permission),
    context: StatelessRunningContext = Depends(create_running_context),
    resource_service: ResourceService = Depends(get_resource_service_dep)
):
    """Add a web resource with configurable content loading options"""
    tenant_id = current_user.tenant_id
    if not tenant_id:
        raise HTTPException(status_code=400, detail="Tenant id missing in user context")
    
    # Validate URL
    if not url.startswith(('http://', 'https://')):
        raise HTTPException(status_code=400, detail="URL must start with http:// or https://")
    
    # Parse exclude_dirs
    exclude_dirs_list = [dir.strip() for dir in exclude_dirs.split(",")] if exclude_dirs else None
    
    # Create the web resource record
    from models.resource import WebResource
    import datetime
    resource_data = WebResource(
        title=title,
        description=description,
        owner_id=current_user.id,
        tenant_id=current_user.tenant_id,
        url=url,
        content="",  # Will be updated after processing
        last_crawled=datetime.datetime.now()
    )
    
    resource = await resource_service.create_web_resource(
        resource=resource_data,
        current_user=current_user,
        tenant_id=tenant_id
    )
    
    try:
        # Process the web resource using the comprehensive indexer
        result = await resource_service.process_web_resource(
            url=url,
            resource_id=str(resource.id),
            current_user=current_user,
            tenant_id=tenant_id,
            load_type=load_type,
            max_depth=max_depth,
            exclude_dirs=exclude_dirs_list,
            sitemap_url=sitemap_url,
            process_immediately=process_immediately,
            metadata={
                "title": title,
                "description": description,
            }
        )
        
        return JSONResponse({
            "message": "Web resource added and processed successfully",
            "resource": resource.model_dump(),
            "processing_result": result
        }, status_code=201)
        
    except Exception as e:
        logger.error(f"Error processing web resource: {str(e)}")
        # Update resource status to failed
        await resource_service.update_resource_status(
            resource_type="web",
            resource_id=str(resource.id),
            status=ResourceStatus.FAILED,
            message=f"Processing failed: {str(e)}"
        )
        raise HTTPException(status_code=500, detail=f"Failed to process web resource: {str(e)}")

@router.get("/search")
async def search_resources(
    query: str,
    resource_type: str,
    k: int = 4,
    current_user: StatelessUser = Depends(get_current_user),
    permission_check: StatelessUser = Depends(search_resource_permission),
    resource_service = Depends(get_resource_service_dep)
):
    """Search resources by type with semantic search"""
    tenant_id = current_user.tenant_id
    if not tenant_id:
        raise HTTPException(status_code=400, detail="Tenant id missing in user context")
    
    try:
        # Validate resource type
        resource_type_enum = ResourceType(resource_type.lower())
    except ValueError:
        raise HTTPException(status_code=400, detail=f"Invalid resource type: {resource_type}")
    
    try:
        results = await resource_service.search_resources(
            query=query,
            tenant_id=tenant_id,
            resource_type=resource_type_enum,
            current_user=current_user,
            k=k
        )
        
        # Convert results to dict format
        formatted_results = []
        for doc in results:
            formatted_results.append({
                "content": doc.page_content,
                "metadata": doc.metadata,
                "score": getattr(doc, 'score', None)  # Some vector stores include relevance scores
            })
        
        return JSONResponse({
            "query": query,
            "resource_type": resource_type,
            "results_count": len(formatted_results),
            "results": formatted_results
        })
        
    except Exception as e:
        logger.error(f"Error searching resources: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Search failed: {str(e)}")

@router.get("/stats")
async def get_resource_stats(
    resource_type: Optional[str] = None,
    current_user: StatelessUser = Depends(get_current_user),
    permission_check: StatelessUser = Depends(read_resource_permission),
    resource_service = Depends(get_resource_service_dep)
):
    """Get statistics for resources by type"""
    tenant_id = current_user.tenant_id
    if not tenant_id:
        raise HTTPException(status_code=400, detail="Tenant id missing in user context")
    
    try:
        if resource_type:
            # Get stats for specific resource type
            try:
                resource_type_enum = ResourceType(resource_type.lower())
            except ValueError:
                raise HTTPException(status_code=400, detail=f"Invalid resource type: {resource_type}")
            
            stats = await resource_service.get_resource_stats(
                resource_type=resource_type_enum,
                tenant_id=tenant_id,
                current_user=current_user
            )
            return JSONResponse(stats)
        else:
            # Get stats for all resource types
            all_stats = {}
            for rt in ResourceType:
                stats = await resource_service.get_resource_stats(
                    resource_type=rt,
                    tenant_id=tenant_id,
                    current_user=current_user
                )
                all_stats[rt.value] = stats
            
            return JSONResponse({
                "tenant_id": tenant_id,
                "resource_stats": all_stats
            })
            
    except Exception as e:
        logger.error(f"Error getting resource stats: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get stats: {str(e)}")

@router.get("/supported-formats")
async def get_supported_formats(resource_service = Depends(get_resource_service_dep)):
    """Get list of supported file formats and web loading options"""
    return JSONResponse({
        "file_formats": {
            "documents": [".pdf", ".docx", ".doc", ".txt", ".md", ".html", ".htm"],
            "data": [".csv", ".xlsx", ".xls", ".json"],
            "presentations": [".pptx", ".ppt"],
            "images": [".png", ".jpg", ".jpeg", ".gif", ".bmp", ".tiff"],
            "other": ["Any text-based format will be processed as plain text"]
        },
        "web_loading_options": {
            "single": "Load content from a single web page",
            "recursive": "Recursively crawl linked pages up to specified depth",
            "sitemap": "Load pages listed in a sitemap.xml file"
        },
        "processing_features": [
            "Automatic format detection",
            "Intelligent text chunking",
            "Metadata extraction",
            "Vector embedding generation",
            "Tenant-isolated storage",
            "Semantic search capabilities"
        ]
    })

@router.get("/{resource_type}")
async def list_resources(
    resource_type: str,
    skip: int = 0,
    limit: int = 10,
    current_user: StatelessUser = Depends(get_current_user),
    context: StatelessRunningContext = Depends(create_running_context),
    resource_service = Depends(get_resource_service_dep)
):
    """List resources by type with pagination"""
    tenant_id = current_user.tenant_id
    if not tenant_id:
        raise HTTPException(status_code=400, detail="Tenant id missing in user context")
    
    try:
        # Validate resource type
        resource_type_enum = ResourceType(resource_type.lower())
    except ValueError:
        raise HTTPException(status_code=400, detail=f"Invalid resource type: {resource_type}")
    
    try:
        # Get resources from appropriate repository
        resources = None
        if resource_type_enum == ResourceType.FILE:
            resources = await resource_service.list_file_resources(
                current_user=current_user,
                skip=skip,
                limit=limit
            )
        elif resource_type_enum == ResourceType.ARTICLE:
            resources = await resource_service.list_article_resources(
                current_user=current_user,
                skip=skip,
                limit=limit
            )
        elif resource_type_enum == ResourceType.WEB:
            resources = await resource_service.list_web_resources(
                current_user=current_user,
                skip=skip,
                limit=limit
            )
        else:
            raise HTTPException(status_code=400, detail="Unsupported resource type")
        
        # Validate and convert to dict format
        if resources is None:
            logger.warning(f"Resource service returned None for {resource_type}")
            resources_data = []
        elif not isinstance(resources, list):
            logger.error(f"Resource service returned non-list type: {type(resources)} for {resource_type}")
            raise HTTPException(status_code=500, detail=f"Invalid response from resource service")
        else:
            resources_data = []
            for resource in resources:
                try:
                    if hasattr(resource, 'model_dump'):
                        resources_data.append(resource.model_dump(mode='json'))
                    else:
                        # Fallback for objects without model_dump method
                        logger.warning(f"Resource object without model_dump: {type(resource)}")
                        resources_data.append(dict(resource) if hasattr(resource, '__dict__') else str(resource))
                except Exception as dump_error:
                    logger.error(f"Error converting resource to dict: {dump_error}")
                    # Skip problematic resources instead of failing the entire request
                    continue
        
        return JSONResponse({
            "resource_type": resource_type,
            "total_returned": len(resources_data),
            "skip": skip,
            "limit": limit,
            "resources": resources_data
        })
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error listing resources: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Failed to list resources: {str(e)}")

@router.get("/{resource_type}/{resource_id}")
async def get_resource_by_id(
    resource_type: str,
    resource_id: str,
    current_user: StatelessUser = Depends(get_current_user),
    permission_check: StatelessUser = Depends(read_resource_permission),
    context: StatelessRunningContext = Depends(create_running_context),
    resource_service = Depends(get_resource_service_dep)
):
    """Get a specific resource by ID and type"""
    tenant_id = current_user.tenant_id
    if not tenant_id:
        raise HTTPException(status_code=400, detail="Tenant id missing in user context")
    
    try:
        # Validate resource type
        resource_type_enum = ResourceType(resource_type.lower())
    except ValueError:
        raise HTTPException(status_code=400, detail=f"Invalid resource type: {resource_type}")
    
    try:
        # Convert string ID to UUID
        resource_id_uuid = UUID(resource_id)
        
        # Get the resource
        if resource_type_enum == ResourceType.FILE:
            resource = await resource_service.get_file_resource(str(resource_id_uuid), current_user)
        elif resource_type_enum == ResourceType.ARTICLE:
            resource = await resource_service.get_article_resource(str(resource_id_uuid), current_user)
        elif resource_type_enum == ResourceType.WEB:
            resource = await resource_service.get_web_resource(str(resource_id_uuid), current_user)
        else:
            raise HTTPException(status_code=400, detail=f"Unsupported resource type: {resource_type}")
        
        if not resource:
            raise HTTPException(status_code=404, detail=f"{resource_type} resource not found")
        
        return JSONResponse({"resource": resource.model_dump()}, status_code=200)
    except ValueError as e:
        # Handle invalid UUID
        raise HTTPException(status_code=400, detail=f"Invalid resource ID: {str(e)}")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting resource: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get resource: {str(e)}")

@router.delete("/{resource_type}/{resource_id}")
async def delete_resource(
    resource_type: str,
    resource_id: str,
    current_user: StatelessUser = Depends(get_current_user),
    permission_check: StatelessUser = Depends(delete_resource_permission),
    resource_service = Depends(get_resource_service_dep)
):
    """Delete a resource by ID and type"""
    tenant_id = current_user.tenant_id
    if not tenant_id:
        raise HTTPException(status_code=400, detail="Tenant id missing in user context")
    
    try:
        # Convert string ID to UUID
        resource_id_uuid = UUID(resource_id)
        
        # Delete the resource
        success = await resource_service.delete_resource(
            resource_type=resource_type,
            resource_id=resource_id_uuid,
            current_user=current_user,
            tenant_id=tenant_id
        )
        
        if not success:
            raise HTTPException(status_code=404, detail=f"{resource_type} resource not found")
            
        return JSONResponse({"message": f"{resource_type} resource successfully deleted"}, status_code=200)
    except ValueError as e:
        # Handle invalid UUID
        raise HTTPException(status_code=400, detail=f"Invalid resource ID: {str(e)}")
    except Exception as e:
        logger.error(f"Error deleting resource: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/stats/{resource_type}")
async def get_resource_stats(
    resource_type: ResourceType,
    current_user: StatelessUser = Depends(get_current_user),
    resource_service = Depends(get_resource_service_dep)
):
    """Get statistics for resources of a specific type"""
    tenant_id = current_user.tenant_id
    if not tenant_id:
        raise HTTPException(status_code=400, detail="Tenant id missing in user context")
    
    try:
        stats = await resource_service.get_resource_stats(resource_type, tenant_id)
        return JSONResponse(stats, status_code=200)
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error getting resource stats: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.patch("/{resource_type}/{resource_id}")
async def update_resource(
    resource_type: ResourceType,
    resource_id: str,
    title: Optional[str] = Form(None),
    description: Optional[str] = Form(None),
    current_user: StatelessUser = Depends(get_current_user),
    permission_check: StatelessUser = Depends(update_resource_permission),
    resource_service = Depends(get_resource_service_dep)
):
    """Update a resource's metadata"""
    try:
        resource = await resource_service.get_resource(resource_type, resource_id)
        if not resource:
            raise HTTPException(status_code=404, detail="Resource not found")
        
        updated_resource = await resource_service.update_resource(
            resource_type=resource_type,
            resource_id=resource_id,
            title=title,
            description=description
        )
        
        if not updated_resource:
            raise HTTPException(status_code=404, detail="Resource not found")
        
        return JSONResponse({
            "message": "Resource updated successfully",
            "resource": updated_resource.model_dump()
        }, status_code=200)
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error updating resource: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/{resource_type}/{resource_id}/status")
async def get_resource_status(
    resource_type: ResourceType,
    resource_id: str,
    current_user: StatelessUser = Depends(get_current_user),
    permission_check: StatelessUser = Depends(read_resource_permission),
    resource_service = Depends(get_resource_service_dep)
):
    """Get the current status of a resource with enhanced tracking information"""
    try:
        resource = await resource_service.get_resource(resource_type, resource_id)
        if not resource:
            raise HTTPException(status_code=404, detail="Resource not found")

        # Basic status information
        status_info = {
            "resource_id": resource_id,
            "resource_type": resource_type.value,
            "status": resource.status,
            "created_at": resource.created_at,
            "updated_at": resource.updated_at
        }

        # Basic information for WebResource
        if resource_type == ResourceType.WEB:
            status_info.update({
                "parent_url": getattr(resource, 'parent_url', None),
                "is_sitemap_child": resource.is_sitemap_child,
                "sitemap_url": resource.sitemap_url,
                "crawl_strategy": resource.crawl_strategy,
                "retry_count": resource.retry_count,
                "last_error": resource.last_error
            })

        return JSONResponse(status_info, status_code=200)
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error getting resource status: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/{resource_type}/{resource_id}/progress")
async def get_resource_progress(
    resource_type: ResourceType,
    resource_id: str,
    current_user: StatelessUser = Depends(get_current_user),
    permission_check: StatelessUser = Depends(read_resource_permission),
    resource_service = Depends(get_resource_service_dep)
):
    """Get detailed progress information for a resource"""
    try:
        resource = await resource_service.get_resource(resource_type, resource_id)
        if not resource:
            raise HTTPException(status_code=404, detail="Resource not found")

        progress_info = {
            "resource_id": resource_id,
            "status": resource.status,
            "progress_percentage": 0,
            "is_complete": resource.status in ["processed", "failed"]
        }

        # Enhanced progress for WebResource
        if resource_type == ResourceType.WEB and hasattr(resource, 'progress_percentage'):
            progress_info.update({
                "progress_percentage": resource.progress_percentage or 0,
                "status_message": resource.status_message,
                "pages_discovered": resource.pages_discovered,
                "pages_processed": resource.pages_processed,
                "pages_failed": resource.pages_failed,
                "estimated_completion_time": resource.estimated_completion_time,
                "processing_start_time": resource.processing_start_time,
                "time_remaining": None
            })

            # Calculate time remaining
            if (resource.estimated_completion_time and
                resource.status == "processing" and
                not resource.is_processing_complete):
                from datetime import datetime, timezone
                now = datetime.now(timezone.utc)
                if resource.estimated_completion_time > now:
                    time_remaining = (resource.estimated_completion_time - now).total_seconds()
                    progress_info["time_remaining"] = time_remaining

        return JSONResponse(progress_info, status_code=200)
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error getting resource progress: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/web/{resource_id}/children/status")
async def get_web_resource_children_status(
    resource_id: str,
    current_user: StatelessUser = Depends(get_current_user),
    permission_check: StatelessUser = Depends(read_resource_permission),
    resource_service = Depends(get_resource_service_dep)
):
    """Get status summary for all child resources of a web resource"""
    try:
        # Get the parent resource
        parent_resource = await resource_service.get_web_resource(resource_id)
        if not parent_resource:
            raise HTTPException(status_code=404, detail="Parent resource not found")

        # Get all child resources
        children = await resource_service.get_web_resource_children(resource_id)

        # Calculate status summary
        status_summary = {
            "parent_resource_id": resource_id,
            "total_children": len(children),
            "status_breakdown": {
                "pending": 0,
                "processing": 0,
                "processed": 0,
                "failed": 0
            },
            "overall_progress": 0,
            "children": []
        }

        for child in children:
            status = child.status.value if hasattr(child.status, 'value') else str(child.status)
            status_summary["status_breakdown"][status] = status_summary["status_breakdown"].get(status, 0) + 1

            child_info = {
                "resource_id": str(child.id),
                "url": child.url,
                "title": child.title,
                "status": status,
                "progress_percentage": getattr(child, 'progress_percentage', None),
                "last_updated": child.updated_at
            }
            status_summary["children"].append(child_info)

        # Calculate overall progress
        if status_summary["total_children"] > 0:
            completed = status_summary["status_breakdown"]["processed"] + status_summary["status_breakdown"]["failed"]
            status_summary["overall_progress"] = (completed / status_summary["total_children"]) * 100

        return JSONResponse(status_summary, status_code=200)
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error getting children status: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/web/{resource_id}/sitemap/details")
async def get_sitemap_processing_details(
    resource_id: str,
    current_user: StatelessUser = Depends(get_current_user),
    permission_check: StatelessUser = Depends(read_resource_permission),
    resource_service = Depends(get_resource_service_dep)
):
    """Get detailed sitemap processing information including metadata and error details"""
    try:
        # Get the parent resource
        parent_resource = await resource_service.get_web_resource(resource_id)
        if not parent_resource:
            raise HTTPException(status_code=404, detail="Resource not found")

        if parent_resource.parent_url or parent_resource.crawl_strategy != "sitemap":
            raise HTTPException(status_code=400, detail="Resource is not a sitemap root")

        # Get all child resources with detailed information
        children = await resource_service.get_web_resource_children(resource_id)

        # Organize children by sitemap depth and parent sitemap
        sitemap_hierarchy = {}
        failed_urls = []

        for child in children:
            depth = getattr(child, 'sitemap_depth', 0)
            parent_sitemap = getattr(child, 'parent_sitemap_url', parent_resource.sitemap_url)

            if depth not in sitemap_hierarchy:
                sitemap_hierarchy[depth] = {}
            if parent_sitemap not in sitemap_hierarchy[depth]:
                sitemap_hierarchy[depth][parent_sitemap] = []

            child_info = {
                "resource_id": str(child.id),
                "url": child.url,
                "title": child.title,
                "status": child.status.value if hasattr(child.status, 'value') else str(child.status),
                "crawl_order": getattr(child, 'crawl_order', None),
                "lastmod": getattr(child, 'lastmod', None),
                "changefreq": getattr(child, 'changefreq', None),
                "priority": getattr(child, 'priority', None),
                "retry_count": getattr(child, 'retry_count', 0),
                "last_error": getattr(child, 'last_error', None),
                "created_at": child.created_at,
                "updated_at": child.updated_at
            }

            sitemap_hierarchy[depth][parent_sitemap].append(child_info)

            # Collect failed URLs
            if child.status == "failed":
                failed_urls.append({
                    "url": child.url,
                    "error": getattr(child, 'last_error', 'Unknown error'),
                    "retry_count": getattr(child, 'retry_count', 0)
                })

        # Calculate detailed statistics
        total_children = len(children)
        status_counts = {}
        for child in children:
            status = child.status.value if hasattr(child.status, 'value') else str(child.status)
            status_counts[status] = status_counts.get(status, 0) + 1

        processing_details = {
            "resource_id": resource_id,
            "url": parent_resource.url,
            "is_root": not parent_resource.parent_url,
            "crawl_strategy": parent_resource.crawl_strategy,
            "status": parent_resource.status.value if hasattr(parent_resource.status, 'value') else str(parent_resource.status),
            "estimated_completion_time": getattr(parent_resource, 'estimated_completion_time', None),
            "total_children": total_children,
            "pages_discovered": getattr(parent_resource, 'pages_discovered', total_children),
            "pages_processed": getattr(parent_resource, 'pages_processed', status_counts.get('processed', 0)),
            "pages_failed": getattr(parent_resource, 'pages_failed', status_counts.get('failed', 0)),
            "success_rate": getattr(parent_resource, 'success_rate', None),
            "status_breakdown": status_counts,
            "sitemap_hierarchy": sitemap_hierarchy,
            "failed_urls": failed_urls,
            "error_details": getattr(parent_resource, 'error_details', {}),
            "last_error": getattr(parent_resource, 'last_error', None)
        }

        return JSONResponse(processing_details, status_code=200)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting sitemap processing details: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/test/auth")
async def test_auth_endpoint(
    current_user: StatelessUser = Depends(get_current_user)
):
    return {
        "message": "Authentication successful",
        "user_id": str(current_user.id),
        "email": current_user.email,
        "tenant_id": str(current_user.tenant_id) if current_user.tenant_id else None
    }

@router.get("/test/simple")
async def test_simple_endpoint():
    """Very simple test endpoint without authentication"""
    return {"message": "Simple endpoint working", "status": "ok"}

@router.post("/{resource_type}/reindex")
async def reindex_resources_endpoint(
    resource_type: ResourceType,
    force_reindex: bool = Form(False),
    current_user: StatelessUser = Depends(get_current_user),
    permission_check: StatelessUser = Depends(update_resource_permission),
    context: StatelessRunningContext = Depends(create_running_context),
    resource_service = Depends(get_resource_service_dep)
):
    """Reindex all resources of a specific type for the current tenant"""
    tenant_id = current_user.tenant_id
    if not tenant_id:
        raise HTTPException(status_code=400, detail="Tenant id missing in user context")
    
    try:
        result = await resource_service.reindex_resources(
            resource_type=resource_type,
            tenant_id=tenant_id,
            current_user=current_user,
            force_reindex=force_reindex
        )
        
        return JSONResponse({
            "message": "Reindex operation completed",
            "resource_type": resource_type.value,
            "result": result
        })
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Reindex operation failed: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Reindex failed: {str(e)}")


@router.post("/{resource_type}/{resource_id}/reindex")
async def reindex_single_resource_endpoint(
    resource_type: ResourceType,
    resource_id: str,
    current_user: StatelessUser = Depends(get_current_user),
    permission_check: StatelessUser = Depends(update_resource_permission),
    context: StatelessRunningContext = Depends(create_running_context),
    resource_service: ResourceService = Depends(get_resource_service_dep)
):
    """Reindex a single resource"""
    
    try:
        result = await resource_service.reindex_single_resource(
            resource_type=resource_type,
            resource_id=resource_id,
            current_user=current_user
        )
        
        return JSONResponse({
            "message": "Resource reindexed successfully",
            "resource_id": resource_id,
            "resource_type": resource_type.value,
            "result": result
        })
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Single resource reindex failed: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Reindex failed: {str(e)}")

@router.post("/bulk/reindex")
async def reindex_selected_resources_endpoint(
    resource_ids: str = Form(..., description="Comma-separated list of resource IDs with type prefix (e.g., 'file:123,web:456,article:789')"),
    current_user: StatelessUser = Depends(get_current_user),
    permission_check: StatelessUser = Depends(update_resource_permission),
    context: StatelessRunningContext = Depends(create_running_context),
    resource_service = Depends(get_resource_service_dep)
):
    """Reindex multiple selected resources across different types"""
    tenant_id = current_user.tenant_id
    if not tenant_id:
        raise HTTPException(status_code=400, detail="Tenant id missing in user context")
    
    try:
        # Parse resource IDs with type prefixes
        parsed_resources = []
        for resource_entry in resource_ids.split(','):
            if ':' not in resource_entry:
                raise HTTPException(status_code=400, detail=f"Invalid resource format: {resource_entry}. Expected 'type:id'")
            
            resource_type_str, resource_id = resource_entry.strip().split(':', 1)
            try:
                resource_type = ResourceType(resource_type_str.lower())
            except ValueError:
                raise HTTPException(status_code=400, detail=f"Invalid resource type: {resource_type_str}")
            
            parsed_resources.append({
                "type": resource_type,
                "id": resource_id
            })
        
        if not parsed_resources:
            raise HTTPException(status_code=400, detail="No valid resources provided")
        
        # Process resources in parallel for better performance
        results = []
        
        async def reindex_single(resource_data):
            try:
                result = await resource_service.reindex_single_resource(
                    resource_type=resource_data["type"],
                    resource_id=resource_data["id"],
                    current_user=current_user
                )
                return {
                    "resource_id": resource_data["id"],
                    "resource_type": resource_data["type"].value,
                    "status": "success",
                    "result": result
                }
            except Exception as e:
                logger.error(f"Failed to reindex resource {resource_data['id']}: {str(e)}")
                return {
                    "resource_id": resource_data["id"],
                    "resource_type": resource_data["type"].value,
                    "status": "failed",
                    "error": str(e)
                }
        
        # Execute all reindex operations in parallel
        results = await asyncio.gather(*[reindex_single(resource_data) for resource_data in parsed_resources], return_exceptions=False)
        
        # Count successes and failures
        successful = [r for r in results if r["status"] == "success"]
        failed = [r for r in results if r["status"] == "failed"]
        
        return JSONResponse({
            "message": f"Bulk reindex completed: {len(successful)} successful, {len(failed)} failed",
            "total_requested": len(parsed_resources),
            "successful_count": len(successful),
            "failed_count": len(failed),
            "results": results
        })
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Bulk reindex operation failed: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Bulk reindex failed: {str(e)}")

# Web Resource Parent-Child Relationship Endpoints

@router.get("/web/{resource_id}/children")
async def get_web_resource_children(
    resource_id: str,
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    current_user: StatelessUser = Depends(get_current_user),
    permission_check: StatelessUser = Depends(read_resource_permission),
    context: StatelessRunningContext = Depends(create_running_context),
    resource_service = Depends(get_resource_service_dep)
):
    """Get all child resources for a parent WebResource"""
    try:
        children = await resource_service.get_web_resource_children(
            parent_id=resource_id,
            current_user=current_user,
            skip=skip,
            limit=limit
        )
        
        return JSONResponse({
            "children": [child.model_dump(mode='json') for child in children],
            "count": len(children),
            "skip": skip,
            "limit": limit
        })
        
    except Exception as e:
        logger.error(f"Error getting web resource children: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get children: {str(e)}")

@router.get("/web/{resource_id}/hierarchy")
async def get_web_resource_hierarchy(
    resource_id: str,
    current_user: StatelessUser = Depends(get_current_user),
    permission_check: StatelessUser = Depends(read_resource_permission),
    resource_service = Depends(get_resource_service_dep)
):
    """Get the complete hierarchy for a root WebResource"""
    try:
        hierarchy = await resource_service.get_web_resource_hierarchy(
            root_id=resource_id,
            current_user=current_user
        )
        
        if "error" in hierarchy:
            raise HTTPException(status_code=404, detail=hierarchy["error"])
        
        return JSONResponse({
            "hierarchy": hierarchy,
            "message": f"Retrieved hierarchy with {hierarchy['total_children']} total children"
        })
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting web resource hierarchy: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get hierarchy: {str(e)}")

@router.get("/web/by-url")
async def get_web_resource_by_url(
    url: str = Query(..., description="The URL to search for"),
    current_user: StatelessUser = Depends(get_current_user),
    permission_check: StatelessUser = Depends(read_resource_permission),
    resource_service = Depends(get_resource_service_dep)
):
    """Get a WebResource by its URL"""
    try:
        resource = await resource_service.get_web_resource_by_url(
            url=url,
            current_user=current_user
        )
        
        if not resource:
            raise HTTPException(status_code=404, detail="Web resource not found for the given URL")
        
        return JSONResponse({
            "resource": resource.model_dump(),
            "message": "Web resource found"
        })
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting web resource by URL: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get resource by URL: {str(e)}")